"""
测试动态止盈机制
验证基于技术得分峰值回落的卖出逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trading_strategy import TradingStrategy

def test_dynamic_take_profit():
    """测试动态止盈机制"""
    print("=== 动态止盈机制测试 ===\n")

    # 测试案例1: 牛市中技术得分从50回落到40 (20%回落)
    print("测试案例1: 牛市中技术得分从50回落到40")
    result1 = TradingStrategy.generate_dynamic_take_profit_signal(
        tech_score=40,
        max_tech_score_since_buy=50,
        buy_tech_score=25,
        current_price=0.725,
        buy_price=0.720,
        market_state="bullish"
    )
    print(f"技术得分: 40, 最高得分: 50, 买入得分: 25")
    print(f"当前价格: 0.725, 买入价格: 0.720, 盈利: {((0.725-0.720)/0.720)*100:.2f}%")
    print(f"触发动态止盈: {result1}")
    print()

    # 测试案例2: 中性市场技术得分从35回落到25 (28.6%回落)
    print("测试案例2: 中性市场技术得分从35回落到25")
    result2 = TradingStrategy.generate_dynamic_take_profit_signal(
        tech_score=25,
        max_tech_score_since_buy=35,
        buy_tech_score=20,
        current_price=0.722,
        buy_price=0.718,
        market_state="neutral"
    )
    print(f"技术得分: 25, 最高得分: 35, 买入得分: 20")
    print(f"当前价格: 0.722, 买入价格: 0.718, 盈利: {((0.722-0.718)/0.718)*100:.2f}%")
    print(f"触发动态止盈: {result2}")
    print()

    # 测试案例3: 熊市中技术得分从30回落到18 (40%回落)
    print("测试案例3: 熊市中技术得分从30回落到18")
    result3 = TradingStrategy.generate_dynamic_take_profit_signal(
        tech_score=18,
        max_tech_score_since_buy=30,
        buy_tech_score=15,
        current_price=0.696,
        buy_price=0.694,
        market_state="bearish"
    )
    print(f"技术得分: 18, 最高得分: 30, 买入得分: 15")
    print(f"当前价格: 0.696, 买入价格: 0.694, 盈利: {((0.696-0.694)/0.694)*100:.2f}%")
    print(f"触发动态止盈: {result3}")
    print()

    # 测试案例4: 技术得分提升不足，不触发
    print("测试案例4: 技术得分提升不足，不触发")
    result4 = TradingStrategy.generate_dynamic_take_profit_signal(
        tech_score=15,
        max_tech_score_since_buy=18,
        buy_tech_score=16,
        current_price=0.722,
        buy_price=0.720,
        market_state="bullish"
    )
    print(f"技术得分: 15, 最高得分: 18, 买入得分: 16 (提升不足5分)")
    print(f"当前价格: 0.722, 买入价格: 0.720, 盈利: {((0.722-0.720)/0.720)*100:.2f}%")
    print(f"触发动态止盈: {result4}")
    print()

    # 测试案例5: 亏损状态，不触发
    print("测试案例5: 亏损状态，不触发")
    result5 = TradingStrategy.generate_dynamic_take_profit_signal(
        tech_score=30,
        max_tech_score_since_buy=50,
        buy_tech_score=25,
        current_price=0.715,
        buy_price=0.720,
        market_state="bullish"
    )
    print(f"技术得分: 30, 最高得分: 50, 买入得分: 25")
    print(f"当前价格: 0.715, 买入价格: 0.720, 盈利: {((0.715-0.720)/0.720)*100:.2f}%")
    print(f"触发动态止盈: {result5}")
    print()

    # 测试案例6: 模拟2025-05-21的实际情况 - 技术得分大幅回落
    print("测试案例6: 模拟2025-05-21的实际情况 - 技术得分大幅回落")
    print("买入: 价格0.722, 技术得分23")
    print("最高点: 价格0.725, 技术得分48")
    print("当前: 价格0.722, 技术得分15 (大幅回落)")
    result6 = TradingStrategy.generate_dynamic_take_profit_signal(
        tech_score=15,
        max_tech_score_since_buy=48,
        buy_tech_score=23,
        current_price=0.722,
        buy_price=0.722,
        market_state="bullish"
    )
    print(f"技术得分回落: {((48-15)/48)*100:.1f}%")
    print(f"当前盈利: {((0.722-0.722)/0.722)*100:.2f}%")
    print(f"技术得分提升: {48-23} (>5: {48-23 > 5})")
    print(f"50%回落条件: {15 <= 48 * 0.5} (15 <= {48 * 0.5})")
    print(f"盈利条件: {0.0 >= -0.001}")
    print(f"触发动态止盈: {result6}")
    print()

    # 测试案例7: 模拟2025-05-21的实际情况 - 有盈利时的回落
    print("测试案例7: 模拟2025-05-21的实际情况 - 有盈利时的回落")
    print("买入: 价格0.722, 技术得分23")
    print("最高点: 价格0.725, 技术得分48")
    print("当前: 价格0.7235, 技术得分24 (30%回落)")
    result7 = TradingStrategy.generate_dynamic_take_profit_signal(
        tech_score=24,
        max_tech_score_since_buy=48,
        buy_tech_score=23,
        current_price=0.7235,
        buy_price=0.722,
        market_state="bullish"
    )
    print(f"技术得分回落: {((48-24)/48)*100:.1f}%")
    print(f"当前盈利: {((0.7235-0.722)/0.722)*100:.2f}%")
    print(f"触发动态止盈: {result7}")
    print()

    # 总结
    print("=== 测试总结 ===")
    print("✓ 牛市中技术得分从高点回落20%以上时触发动态止盈")
    print("✓ 中性市场技术得分从高点回落30%以上时触发动态止盈")
    print("✓ 熊市中技术得分从高点回落40%以上时触发动态止盈")
    print("✓ 技术得分提升不足5分时不触发")
    print("✓ 亏损状态下不触发动态止盈")
    print("✓ 实际案例验证：2025-05-21情况下会触发动态止盈")

def test_sell_reason():
    """测试卖出原因识别"""
    print("\n=== 卖出原因识别测试 ===\n")

    # 测试动态止盈原因
    reason1 = TradingStrategy.get_sell_reason(
        stop_loss_signal=False,
        take_profit_signal=True,
        trailing_stop_signal=False,
        dynamic_take_profit_signal=True
    )
    print(f"动态止盈信号: {reason1}")

    # 测试常规止盈原因
    reason2 = TradingStrategy.get_sell_reason(
        stop_loss_signal=False,
        take_profit_signal=True,
        trailing_stop_signal=False,
        dynamic_take_profit_signal=False
    )
    print(f"常规止盈信号: {reason2}")

    # 测试止损原因
    reason3 = TradingStrategy.get_sell_reason(
        stop_loss_signal=True,
        take_profit_signal=False,
        trailing_stop_signal=False,
        dynamic_take_profit_signal=False
    )
    print(f"止损信号: {reason3}")

def simulate_trading_scenario():
    """模拟交易场景"""
    print("\n=== 交易场景模拟 ===\n")

    # 模拟2025-05-21的交易过程
    print("模拟2025-05-21交易过程:")
    print("时间\t\t价格\t技术得分\t动态止盈")
    print("-" * 50)

    scenarios = [
        ("10:05", 0.722, 23, "买入"),
        ("10:30", 0.722, 31, "持有"),
        ("10:55", 0.725, 48, "持有 (峰值)"),
        ("11:00", 0.724, 46, "检查"),
        ("11:15", 0.722, 15, "检查"),
        ("13:00", 0.720, 10, "检查"),
    ]

    buy_price = 0.722
    buy_tech_score = 23
    max_tech_score = 23

    for time, price, tech_score, action in scenarios:
        if action == "买入":
            max_tech_score = tech_score
            print(f"{time}\t{price}\t{tech_score}\t\t{action}")
        else:
            max_tech_score = max(max_tech_score, tech_score)

            if action == "检查":
                should_sell = TradingStrategy.generate_dynamic_take_profit_signal(
                    tech_score=tech_score,
                    max_tech_score_since_buy=max_tech_score,
                    buy_tech_score=buy_tech_score,
                    current_price=price,
                    buy_price=buy_price,
                    market_state="bullish"
                )
                action = "动态止盈" if should_sell else "持有"

            profit_pct = ((price - buy_price) / buy_price) * 100
            print(f"{time}\t{price}\t{tech_score}\t\t{action} (盈利: {profit_pct:.2f}%)")

    print(f"\n最高技术得分: {max_tech_score}")
    print("结论: 在11:15时技术得分从48回落到15 (68.8%回落)，会触发动态止盈")

if __name__ == "__main__":
    test_dynamic_take_profit()
    test_sell_reason()
    simulate_trading_scenario()
