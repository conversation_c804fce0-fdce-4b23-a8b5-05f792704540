"""
交易策略优化建议实现
基于交易记录分析的具体优化代码
"""

class TradingStrategyOptimization:
    """交易策略优化类"""
    
    @staticmethod
    def dynamic_take_profit_signal(tech_score, max_tech_score_since_buy, market_state, 
                                 current_price, buy_price, min_profit_threshold=0.005):
        """
        动态止盈信号 - 基于技术得分峰值回落
        
        Parameters
        ----------
        tech_score : int
            当前技术得分
        max_tech_score_since_buy : int
            买入后的最高技术得分
        market_state : str
            市场状态
        current_price : float
            当前价格
        buy_price : float
            买入价格
        min_profit_threshold : float
            最小盈利阈值
            
        Returns
        -------
        bool
            是否触发动态止盈
        """
        # 计算当前盈利率
        profit_pct = (current_price - buy_price) / buy_price
        
        # 只有在盈利的情况下才考虑动态止盈
        if profit_pct < min_profit_threshold:
            return False
            
        if market_state == "bullish":
            # 牛市中技术得分从高点回落20%且得分曾达到40以上
            if max_tech_score_since_buy >= 40 and tech_score <= max_tech_score_since_buy * 0.8:
                return True
            # 或者技术得分从高点回落15%且得分曾达到50以上
            elif max_tech_score_since_buy >= 50 and tech_score <= max_tech_score_since_buy * 0.85:
                return True
        elif market_state == "neutral":
            # 中性市场技术得分从高点回落30%且得分曾达到30以上
            if max_tech_score_since_buy >= 30 and tech_score <= max_tech_score_since_buy * 0.7:
                return True
        else:  # bearish
            # 熊市中技术得分从高点回落40%且得分曾达到20以上
            if max_tech_score_since_buy >= 20 and tech_score <= max_tech_score_since_buy * 0.6:
                return True
            # 或者技术得分从高点回落25%且得分曾达到35以上
            elif max_tech_score_since_buy >= 35 and tech_score <= max_tech_score_since_buy * 0.75:
                return True
                
        return False
    
    @staticmethod
    def optimized_sell_conditions(market_state):
        """
        优化的卖出条件要求
        
        Parameters
        ----------
        market_state : str
            市场状态
            
        Returns
        -------
        int
            需要满足的条件数量
        """
        if market_state == "bullish":
            return 2  # 从3降到2，更容易触发卖出
        elif market_state == "bearish":
            return 1  # 保持不变
        else:
            return 2  # 保持不变
    
    @staticmethod
    def optimized_buy_thresholds(market_state):
        """
        优化的买入阈值
        
        Parameters
        ----------
        market_state : str
            市场状态
            
        Returns
        -------
        dict
            包含技术得分阈值和额外条件的字典
        """
        if market_state == "bullish":
            return {
                "tech_threshold": 3,  # 保持不变
                "rsi_range": (25, 70),
                "additional_condition": None
            }
        elif market_state == "bearish":
            return {
                "tech_threshold": 10,  # 从6提高到10
                "rsi_range": (20, 35),  # 更严格的RSI范围
                "additional_condition": "strong_reversal"  # 需要强反转信号
            }
        else:  # neutral
            return {
                "tech_threshold": 5,  # 从4提高到5
                "rsi_range": (25, 65),
                "additional_condition": None
            }
    
    @staticmethod
    def time_based_exit_signal(hold_duration_hours, market_state, profit_pct, 
                             tech_score, max_tech_score_since_buy):
        """
        基于时间的退出信号
        
        Parameters
        ----------
        hold_duration_hours : float
            持有时间（小时）
        market_state : str
            市场状态
        profit_pct : float
            盈利百分比
        tech_score : int
            当前技术得分
        max_tech_score_since_buy : int
            买入后最高技术得分
            
        Returns
        -------
        bool
            是否触发时间基础退出
        """
        if market_state == "bearish":
            # 熊市中持有超过24小时且无明显盈利时退出
            if hold_duration_hours >= 24 and profit_pct < 0.005:
                return True
            # 或者持有超过48小时且亏损时退出
            elif hold_duration_hours >= 48 and profit_pct < 0:
                return True
        elif market_state == "neutral":
            # 中性市场持有超过48小时且亏损时退出
            if hold_duration_hours >= 48 and profit_pct < 0:
                return True
            # 或者持有超过72小时且收益微薄时退出
            elif hold_duration_hours >= 72 and profit_pct < 0.01:
                return True
        else:  # bullish
            # 牛市中持有超过72小时且技术得分持续下降时退出
            if (hold_duration_hours >= 72 and 
                tech_score < max_tech_score_since_buy * 0.5 and 
                profit_pct < 0.02):
                return True
                
        return False
    
    @staticmethod
    def enhanced_stop_loss(current_price, buy_price, max_price_since_buy, 
                          market_state, tech_score, hold_duration_hours):
        """
        增强的止损机制
        
        Parameters
        ----------
        current_price : float
            当前价格
        buy_price : float
            买入价格
        max_price_since_buy : float
            买入后最高价格
        market_state : str
            市场状态
        tech_score : int
            当前技术得分
        hold_duration_hours : float
            持有时间
            
        Returns
        -------
        bool
            是否触发增强止损
        """
        profit_pct = (current_price - buy_price) / buy_price
        
        # 基础止损
        basic_stop_loss_pct = {
            "bullish": -0.03,    # 牛市3%止损
            "neutral": -0.025,   # 中性市场2.5%止损
            "bearish": -0.02     # 熊市2%止损
        }
        
        if profit_pct <= basic_stop_loss_pct[market_state]:
            return True
        
        # 技术得分恶化止损
        if tech_score <= 0 and profit_pct < 0.01:
            return True
        
        # 长时间持有且技术得分低的止损
        if hold_duration_hours >= 48 and tech_score <= 5 and profit_pct < 0:
            return True
            
        # 利润回吐止损
        if max_price_since_buy > buy_price:
            max_profit_pct = (max_price_since_buy - buy_price) / buy_price
            current_drawdown = (current_price - max_price_since_buy) / max_price_since_buy
            
            # 如果曾经盈利超过2%，现在回撤超过50%则止损
            if max_profit_pct > 0.02 and current_drawdown <= -0.5:
                return True
                
        return False
    
    @staticmethod
    def get_optimization_parameters():
        """
        获取优化后的策略参数
        
        Returns
        -------
        dict
            优化后的策略参数
        """
        return {
            "buy_thresholds": {
                "bullish": {"tech_threshold": 3, "rsi_range": (25, 70)},
                "neutral": {"tech_threshold": 5, "rsi_range": (25, 65)},
                "bearish": {"tech_threshold": 10, "rsi_range": (20, 35)}
            },
            "sell_conditions": {
                "bullish": {"required_conditions": 2},
                "neutral": {"required_conditions": 2},
                "bearish": {"required_conditions": 1}
            },
            "stop_loss_pct": {
                "bullish": 0.03,
                "neutral": 0.025,
                "bearish": 0.02
            },
            "dynamic_take_profit": {
                "enabled": True,
                "min_profit_threshold": 0.005
            },
            "time_based_exit": {
                "enabled": True,
                "max_hold_hours": {
                    "bullish": 72,
                    "neutral": 48,
                    "bearish": 24
                }
            }
        }

def apply_optimizations_to_strategy():
    """
    应用优化建议到现有策略的示例代码
    """
    print("=== 交易策略优化建议 ===")
    print("\n1. 动态止盈机制:")
    print("   - 跟踪买入后最高技术得分")
    print("   - 技术得分从峰值回落时触发卖出")
    print("   - 不同市场状态使用不同回落阈值")
    
    print("\n2. 优化卖出条件:")
    print("   - 牛市: 需要满足的条件从3个降到2个")
    print("   - 中性/熊市: 保持现有条件")
    
    print("\n3. 提高熊市买入门槛:")
    print("   - 技术得分阈值从6提高到10")
    print("   - RSI范围收紧到20-35")
    print("   - 需要更强的反转信号")
    
    print("\n4. 时间基础退出:")
    print("   - 熊市: 24小时无盈利或48小时亏损时退出")
    print("   - 中性: 48小时亏损或72小时微利时退出")
    print("   - 牛市: 72小时技术得分大幅下降时退出")
    
    print("\n5. 增强止损机制:")
    print("   - 不同市场状态使用不同止损比例")
    print("   - 技术得分恶化时提前止损")
    print("   - 利润回吐保护机制")

if __name__ == "__main__":
    apply_optimizations_to_strategy()
