"""
交易记录可视化分析脚本
用于分析交易决策的问题并生成优化建议
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta

def load_trading_data(file_path="prediction_results/trading_decisions.csv"):
    """加载交易数据"""
    try:
        df = pd.read_csv(file_path)
        df['datetime'] = pd.to_datetime(df['datetime'])
        return df
    except Exception as e:
        print(f"加载数据失败: {e}")
        return None

def analyze_trading_sessions(df):
    """分析交易会话"""
    sessions = []
    current_session = None

    for idx, row in df.iterrows():
        if row['decision'] == 'buy':
            if current_session is not None:
                sessions.append(current_session)
            current_session = {
                'buy_time': row['datetime'],
                'buy_price': row['price'],
                'buy_tech_score': row['tech_score'],
                'buy_market_state': row['market_state'],
                'max_price': row['price'],
                'max_tech_score': row['tech_score'],
                'sell_time': None,
                'sell_price': None,
                'sell_tech_score': None,
                'sell_reason': None,
                'profit_pct': 0,
                'hold_duration': 0,
                'records': [row.to_dict()]
            }
        elif current_session is not None:
            current_session['records'].append(row.to_dict())
            current_session['max_price'] = max(current_session['max_price'], row['price'])
            current_session['max_tech_score'] = max(current_session['max_tech_score'], row['tech_score'])

            if row['decision'] == 'sell':
                current_session['sell_time'] = row['datetime']
                current_session['sell_price'] = row['price']
                current_session['sell_tech_score'] = row['tech_score']
                current_session['sell_reason'] = row['reason']
                current_session['profit_pct'] = (row['price'] - current_session['buy_price']) / current_session['buy_price'] * 100
                current_session['hold_duration'] = (row['datetime'] - current_session['buy_time']).total_seconds() / 3600
                sessions.append(current_session)
                current_session = None

    # 如果还有未完成的会话
    if current_session is not None:
        last_record = current_session['records'][-1]
        current_session['sell_time'] = last_record['datetime']
        current_session['sell_price'] = last_record['price']
        current_session['sell_tech_score'] = last_record['tech_score']
        current_session['sell_reason'] = "持有中"
        current_session['profit_pct'] = (last_record['price'] - current_session['buy_price']) / current_session['buy_price'] * 100
        current_session['hold_duration'] = (pd.to_datetime(last_record['datetime']) - current_session['buy_time']).total_seconds() / 3600
        sessions.append(current_session)

    return sessions

def identify_missed_opportunities(sessions):
    """识别错失的机会"""
    missed_opportunities = []

    for session in sessions:
        if session['sell_reason'] != "持有中":
            # 计算最佳卖点的潜在收益
            max_potential_profit = (session['max_price'] - session['buy_price']) / session['buy_price'] * 100
            actual_profit = session['profit_pct']
            missed_profit = max_potential_profit - actual_profit

            if missed_profit > 1.0:  # 错失超过1%的收益
                missed_opportunities.append({
                    'session': session,
                    'missed_profit': missed_profit,
                    'max_potential_profit': max_potential_profit,
                    'actual_profit': actual_profit,
                    'max_tech_score': session['max_tech_score'],
                    'sell_tech_score': session['sell_tech_score']
                })

    return missed_opportunities

def generate_analysis_report(df, sessions, missed_opportunities):
    """生成分析报告"""
    print("=== 交易记录分析报告 ===\n")

    # 基本统计
    total_sessions = len([s for s in sessions if s['sell_reason'] != "持有中"])
    profitable_sessions = len([s for s in sessions if s['sell_reason'] != "持有中" and s['profit_pct'] > 0])

    print(f"总交易次数: {total_sessions}")
    print(f"盈利交易次数: {profitable_sessions}")
    print(f"盈利率: {profitable_sessions/total_sessions*100:.1f}%" if total_sessions > 0 else "无完成交易")

    if total_sessions > 0:
        avg_profit = np.mean([s['profit_pct'] for s in sessions if s['sell_reason'] != "持有中"])
        avg_hold_time = np.mean([s['hold_duration'] for s in sessions if s['sell_reason'] != "持有中"])
        print(f"平均收益率: {avg_profit:.2f}%")
        print(f"平均持有时间: {avg_hold_time:.1f}小时")

    # 错失机会分析
    print(f"\n=== 错失机会分析 ===")
    print(f"错失机会次数: {len(missed_opportunities)}")

    if missed_opportunities:
        total_missed_profit = sum([m['missed_profit'] for m in missed_opportunities])
        avg_missed_profit = np.mean([m['missed_profit'] for m in missed_opportunities])
        print(f"总错失收益: {total_missed_profit:.2f}%")
        print(f"平均错失收益: {avg_missed_profit:.2f}%")

        print(f"\n前3个最大错失机会:")
        sorted_missed = sorted(missed_opportunities, key=lambda x: x['missed_profit'], reverse=True)
        for i, missed in enumerate(sorted_missed[:3]):
            session = missed['session']
            print(f"{i+1}. {session['buy_time'].strftime('%Y-%m-%d %H:%M')} - {session['sell_time'].strftime('%Y-%m-%d %H:%M')}")
            print(f"   买入价格: {session['buy_price']:.3f}, 最高价格: {session['max_price']:.3f}, 卖出价格: {session['sell_price']:.3f}")
            print(f"   最大技术得分: {session['max_tech_score']}, 卖出技术得分: {session['sell_tech_score']}")
            print(f"   错失收益: {missed['missed_profit']:.2f}%, 实际收益: {missed['actual_profit']:.2f}%")
            print(f"   卖出原因: {session['sell_reason']}")

    # 市场状态分析
    print(f"\n=== 市场状态分析 ===")
    market_states = {}
    for session in sessions:
        if session['sell_reason'] != "持有中":
            state = session['buy_market_state']
            if state not in market_states:
                market_states[state] = {'count': 0, 'profits': []}
            market_states[state]['count'] += 1
            market_states[state]['profits'].append(session['profit_pct'])

    for state, data in market_states.items():
        avg_profit = np.mean(data['profits'])
        win_rate = len([p for p in data['profits'] if p > 0]) / len(data['profits']) * 100
        print(f"{state}: {data['count']}次交易, 平均收益{avg_profit:.2f}%, 胜率{win_rate:.1f}%")

def plot_trading_analysis(sessions, missed_opportunities):
    """绘制交易分析图表"""
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 1. 收益率分布
    completed_sessions = [s for s in sessions if s['sell_reason'] != "持有中"]
    if completed_sessions:
        profits = [s['profit_pct'] for s in completed_sessions]
        axes[0, 0].hist(profits, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].axvline(x=0, color='red', linestyle='--', label='盈亏平衡线')
        axes[0, 0].set_xlabel('收益率 (%)')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].set_title('交易收益率分布')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

    # 2. 持有时间 vs 收益率
    if completed_sessions:
        hold_times = [s['hold_duration'] for s in completed_sessions]
        profits = [s['profit_pct'] for s in completed_sessions]
        colors = ['red' if p < 0 else 'green' for p in profits]
        axes[0, 1].scatter(hold_times, profits, c=colors, alpha=0.6)
        axes[0, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
        axes[0, 1].set_xlabel('持有时间 (小时)')
        axes[0, 1].set_ylabel('收益率 (%)')
        axes[0, 1].set_title('持有时间 vs 收益率')
        axes[0, 1].grid(True, alpha=0.3)

    # 3. 技术得分 vs 收益率
    if completed_sessions:
        buy_scores = [s['buy_tech_score'] for s in completed_sessions]
        profits = [s['profit_pct'] for s in completed_sessions]
        axes[1, 0].scatter(buy_scores, profits, alpha=0.6, color='purple')
        axes[1, 0].axhline(y=0, color='black', linestyle='-', alpha=0.3)
        axes[1, 0].set_xlabel('买入时技术得分')
        axes[1, 0].set_ylabel('收益率 (%)')
        axes[1, 0].set_title('买入技术得分 vs 收益率')
        axes[1, 0].grid(True, alpha=0.3)

    # 4. 错失机会分析
    if missed_opportunities:
        missed_profits = [m['missed_profit'] for m in missed_opportunities]
        max_scores = [m['max_tech_score'] for m in missed_opportunities]
        axes[1, 1].scatter(max_scores, missed_profits, alpha=0.6, color='orange')
        axes[1, 1].set_xlabel('最高技术得分')
        axes[1, 1].set_ylabel('错失收益 (%)')
        axes[1, 1].set_title('技术得分峰值 vs 错失收益')
        axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('trading_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    # 加载数据
    df = load_trading_data()
    if df is None:
        return

    # 分析交易会话
    sessions = analyze_trading_sessions(df)

    # 识别错失机会
    missed_opportunities = identify_missed_opportunities(sessions)

    # 生成分析报告
    generate_analysis_report(df, sessions, missed_opportunities)

    # 绘制分析图表
    plot_trading_analysis(sessions, missed_opportunities)

    # 输出优化建议
    print(f"\n=== 优化建议 ===")
    print("1. 实施动态止盈机制：当技术得分从峰值回落20-30%时考虑卖出")
    print("2. 降低牛市卖出条件：将需要满足的条件从3个降到2个")
    print("3. 提高熊市买入门槛：技术得分阈值从6提高到10")
    print("4. 添加时间基础退出：防止长时间无效持有")
    print("5. 优化止损机制：根据市场状态调整止损比例")

    print(f"\n详细优化方案请查看 'trading_analysis_report.md' 和 'strategy_optimization.py'")

if __name__ == "__main__":
    main()
